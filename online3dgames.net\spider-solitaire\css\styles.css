* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.landscape-prompt {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a4d2e;
    z-index: 10000;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
}

.landscape-content {
    max-width: 400px;
}

.rotate-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rotate 2s infinite ease-in-out;
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

.landscape-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.landscape-content p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a4d2e;
    min-height: 100vh;
    color: #fff;
    overflow-x: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    min-height: 100dvh;
}

/* Header Styles */
.game-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat {
    background: rgba(255,255,255,0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.stat-label {
    font-weight: bold;
    margin-right: 5px;
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-shadow: none;
    outline: none;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#fullscreenBtn {
    font-size: 18px;
    padding: 8px 12px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#fullscreenBtn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

:fullscreen {
    background: #1a4d2e;
}

:-webkit-full-screen {
    background: #1a4d2e;
}

:-moz-full-screen {
    background: #1a4d2e;
}

:-ms-fullscreen {
    background: #1a4d2e;
}

.game-board {
    display: grid;
    grid-template-areas: 
        "foundation stock"
        "tableau tableau";
    grid-template-columns: 1fr auto;
    gap: 30px;
    margin-bottom: 30px;
}

.foundation-area {
    grid-area: foundation;
    display: flex;
    gap: 15px;
    justify-content: flex-start;
}

.foundation-pile {
    width: 132px;
    height: 185px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    position: relative;
}

.pile-placeholder {
    font-size: 24px;
    opacity: 0.5;
    font-weight: bold;
}

.stock-area {
    grid-area: stock;
    display: flex;
    gap: 8px;
    align-items: flex-start;
    position: relative;
    perspective: 1000px;
    padding: 10px;
}

.stock-pile, .waste-pile {
    width: 100px;
    height: 145px;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.stock-pile {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        2px 0 4px rgba(0,0,0,0.3),
        4px 0 8px rgba(0,0,0,0.2),
        6px 0 12px rgba(0,0,0,0.1);
}

.stock-pile:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.4);
    box-shadow:
        2px 0 6px rgba(0,0,0,0.4),
        4px 0 10px rgba(0,0,0,0.3),
        6px 0 14px rgba(0,0,0,0.2);
}

.card-back {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #8B0000, #DC143C);
    border-radius: 6px;
    border: 2px solid #fff;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
}

.card-back::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    border-radius: 4px;
}

.waste-pile {
    background: rgba(255,255,255,0.05);
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow:
        -2px 0 4px rgba(0,0,0,0.3),
        -4px 0 8px rgba(0,0,0,0.2),
        -6px 0 12px rgba(0,0,0,0.1);
}

.waste-pile:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
}

.tableau-area {
    grid-area: tableau;
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.tableau-pile {
    width: 132px;
    min-height: 185px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    justify-content: center;
}

/* Card Styles */
.card {
    width: 100px;
    height: 145px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ccc;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 6px;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.card.face-down {
    background: linear-gradient(45deg, #8B0000, #DC143C);
    color: transparent;
}

.card.face-down::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    border-radius: 4px;
}

.card.red {
    color: #dc143c;
}

.card.black {
    color: #000;
}

.card-top, .card-bottom {
    display: flex;
    align-items: center;
    gap: 2px;
}

.card-bottom {
    transform: rotate(180deg);
    align-self: flex-end;
}

.card-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

/* Dragging States */
.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6);
    transform: scale(1.05);
    transition: none !important;
}

.card.drag-placeholder {
    opacity: 0.3;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px dashed rgba(255, 255, 255, 0.5) !important;
}

.card.draggable-card {
    cursor: grab;
    transition: all 0.2s ease;
}

.card.draggable-card:hover:not(.dragging) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.card.draggable-card:active {
    cursor: grabbing;
}

.tableau-pile .card.dragging ~ .card.dragging {
    z-index: 9998 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.5);
    transform: scale(1.05);
}

.foundation-pile:hover,
.tableau-pile:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
    transition: all 0.2s ease;
}

.drop-zone-valid {
    background: rgba(0, 255, 0, 0.2) !important;
    border: 2px solid rgba(0, 255, 0, 0.6) !important;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.4) !important;
}

.drop-zone-invalid {
    background: rgba(255, 0, 0, 0.1) !important;
    border: 2px solid rgba(255, 0, 0, 0.4) !important;
}

.card.book-flip {
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
    transform-origin: right center;
}

.card.book-flip-start {
    transform: rotateY(0deg);
}

.card.book-flip-middle {
    transform: rotateY(90deg);
}

.card.book-flip-end {
    transform: rotateY(180deg);
}

.waste-pile .card {
    box-shadow:
        -2px 0 4px rgba(0,0,0,0.3),
        -1px 0 2px rgba(0,0,0,0.2);
}

.stock-pile .card-back {
    box-shadow:
        2px 0 4px rgba(0,0,0,0.3),
        1px 0 2px rgba(0,0,0,0.2),
        inset 0 0 10px rgba(0,0,0,0.3);
}

.stock-count {
    font-family: 'Arial', sans-serif;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    border: 1px solid rgba(255,255,255,0.3);
}

/* Flip animation */
.card.flipping {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.card.flip-start {
    transform: rotateY(180deg);
}

.card.flip-middle {
    transform: rotateY(90deg);
}

.card.flip-end {
    transform: rotateY(0deg);
}

.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    transform: scale(1.05);
}

.card.flying-fast {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
    transform: scale(1.02);
}

.card.moving-to-foundation {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transition: all 0.15s ease;
}

.card.moving-to-tableau {
    transform: scale(1.02);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
    transition: all 0.3s ease;
}

.card.card-returning {
    z-index: 8888 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.3s ease-out !important;
}

.pile.drag-over {
    background: rgba(255,255,0,0.3);
    border-color: #ffff00;
}

/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.message-content {
    background: linear-gradient(135deg, #1a3d0a, #2d5016, #4a7c59);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 450px;
    border: 3px solid #7fb069;
    box-shadow:
        0 0 0 1px rgba(255,255,255,0.1),
        0 10px 30px rgba(0,0,0,0.7),
        inset 0 1px 0 rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.message-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #7fb069, transparent);
}

.message-content h2 {
    margin-bottom: 20px;
    font-size: 2rem;
    color: #fff;
}

.message-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: rgba(255,255,255,0.9);
}

.message-stats {
    margin: 20px 0;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.message-stats div {
    background: rgba(255,255,255,0.1);
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 14px;
}

.message-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-selection {
    margin-bottom: 20px;
}

.difficulty-selection p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 10px;
    font-size: 16px;
}

.difficulty-selection .btn {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.difficulty-selection .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.difficulty-selection .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
}

.difficulty-selection .btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    border-color: #ffc107;
    color: #000;
}

.difficulty-selection .btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    border-color: #dc3545;
}



/* Help Panel */
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.help-content {
    background: linear-gradient(135deg, #2d5016, #4a7c59);
    padding: 0;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
}

.help-header h3 {
    margin: 0;
    font-size: 1.6rem;
    color: #fff;
}

.help-body {
    padding: 20px 30px;
    overflow-y: auto;
    flex: 1;
}

.help-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
    text-align: center;
}

.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    margin-bottom: 10px;
    color: #fff;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255,255,255,0.3);
    padding-bottom: 5px;
}

.help-section p {
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
    margin-bottom: 10px;
}

.help-section ul {
    color: rgba(255,255,255,0.9);
    padding-left: 20px;
}

.help-section li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.help-section strong {
    color: #fff;
}

.challenge-notice {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.5);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.challenge-notice h4 {
    color: #ffc107;
    border-bottom-color: rgba(255, 193, 7, 0.5);
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

/* Hidden Class */
.hidden {
    display: none !important;
}

/* Touch and Mobile Optimizations */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.card {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Mobile viewport optimizations */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
    }

    body {
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .game-container {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
}

.card.draggable-card {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}
@media (max-width:1024px){
    .game-header {
        margin-bottom: 0;
        gap: 5px;
    }

    .game-container{
        padding: 10px;
    }

    .header-left h1{
        font-size: 1.5rem;
    }
    .btn,.stat{
        padding: 5px 10px;
        font-size: 12px;
    }

    #fullscreenBtn {
        font-size: 14px;
        padding: 5px 8px;
        min-width: 36px;
    }

    .game-board{
        gap: 10px;
    }

    .tableau-area{
        margin-top: 0;
    }

    .foundation-pile,.stock-pile, .waste-pile,.tableau-pile,.card{
        width: 66px;
        height: 93px;
        font-size: 14px;
        min-height: 93px;
    }
    .card{
        padding: 0 2px;
    }
}

/* Improve touch targets and interactions for mobile */
@media (max-width: 768px) {
    .card {
        min-height: 60px;
        min-width: 40px;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    }

    .card.draggable-card:hover:not(.dragging) {
        transform: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .card.dragging {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        z-index: 1000;
        opacity: 0.9;
    }

    /* Better touch feedback */
    .card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .tableau-pile {
        min-height: 200px;
        position: relative;
    }

    /* Ensure proper touch area for stock pile */
    .stock-pile {
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    }

    .stock-pile:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .help-content {
        max-width: 95%;
        max-height: 95vh;
    }

    .help-header, .help-body, .help-footer {
        padding: 15px 20px;
    }

    .help-header h3 {
        font-size: 1.3rem;
    }

    .help-section h4 {
        font-size: 1.1rem;
    }

    .help-section ul {
        padding-left: 15px;
    }
}

/* Hint Styles */
.card-hint {
    animation: pulse 1s infinite;
    border: 2px solid #ffff00 !important;
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.7) !important;
}

.pile-hint {
    animation: pulse 1s infinite;
    border-color: #ffff00 !important;
    background: rgba(255, 255, 0, 0.2) !important;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Stock Empty State */
.stock-pile.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
}

.stock-pile.empty:hover {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
}

/* Auto-complete animation */
.auto-move {
    animation: autoMove 0.5s ease-in-out;
}

@keyframes autoMove {
    0% { transform: scale(1); }
    50% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1); }
}

/* Mobile Responsive Design - Keep PC layout structure but scale down */
@media (max-width: 768px) {
    .game-container {
        padding: 5px;
        min-height: 100vh;
        min-height: 100dvh;
        box-sizing: border-box;
    }

    .game-header {
        padding: 8px 10px;
        margin-bottom: 8px;
    }

    .header-left h1 {
        font-size: 1.4rem;
        margin: 0;
    }

    .game-stats {
        gap: 10px;
    }

    .game-stats span {
        font-size: 0.85rem;
        padding: 4px 8px;
    }

    .header-right {
        gap: 6px;
    }

    .btn {
        padding: 6px 10px;
        font-size: 0.75rem;
        min-width: auto;
    }

    #fullscreenBtn {
        font-size: 0.9rem;
        padding: 6px 8px;
        min-width: 32px;
    }

    /* Hide desktop stock area on mobile */
    .desktop-only {
        display: none;
    }
}

/* Floating Stock Button for tablets and mobile */
.btn-stock-floating {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #2c5530, #1a3d1f);
    border: 2px solid #4a7c59;
    color: #fff;
    padding: 8px 12px;
    border-radius: 8px;
    display: none;
    align-items: center;
    justify-content: center;
    gap: 6px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
}

.btn-stock-floating:hover {
    background: linear-gradient(135deg, #3a6b3f, #225229);
    border-color: #5a8c69;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

.btn-stock-floating:disabled {
    background: #666;
    border-color: #888;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-stock-floating .stock-icon {
    font-size: 1.2rem;
}

.btn-stock-floating .stock-count {
    font-size: 0.8rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 2px 6px;
    min-width: 20px;
    text-align: center;
}

/* Show floating stock button on tablets and mobile */
@media (max-width: 1024px) {
    .btn-stock-floating {
        display: flex;
    }

    .desktop-only {
        display: none;
    }
}

/* Desktop styles */
@media (min-width: 1025px) {
    .btn-stock-floating {
        display: none;
    }

    .desktop-only {
        display: block;
    }

    /* Keep PC layout structure but scale down */
    .game-board {
        gap: 8px;
        padding: 5px;
        height: calc(100vh - 120px);
        box-sizing: border-box;
    }

    .foundation-area {
        gap: 4px;
        margin-bottom: 6px;
    }

    .stock-area {
        gap: 8px;
        margin-bottom: 8px;
    }

    .tableau-area {
        gap: 3px;
        flex: 1;
        min-height: 0;
    }

    /* Dynamic card sizing based on screen width */


    

    .card-center {
        font-size: calc(1.2rem + 0.5vw);
    }

    .message-content {
        padding: 15px;
        margin: 10px;
        max-width: 90vw;
    }

    .message-content h2 {
        font-size: 1.3rem;
    }
    
    .message-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .message-buttons,
    .settings-buttons {
        flex-direction: column;
    }

    #fullscreenBtn {
        font-size: 20px;
        padding: 10px 16px;
        min-width: 48px;
    }
}

@media (max-width: 480px) {
    .tableau-area {
        gap: 8px;
    }
    
    .card,
    .foundation-pile,
    .stock-pile,
    .waste-pile,
    .tableau-pile {
        width: 100px;
        height: 133px;
    }

    .card {
        width: 96px;
        height: 129px;
        font-size: 14px;
    }

    .card-center {
        font-size: 24px;
    }
}

.card.no-move-available {
    animation: noMoveShake 0.3s ease-in-out;
}

@keyframes noMoveShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@media screen and (max-width: 768px) and (orientation: portrait) {
    .landscape-prompt {
        display: flex;
    }

    .game-container {
        display: none;
    }
}

@media screen and (max-width: 768px) and (orientation: landscape) {
    .landscape-prompt {
        display: none;
    }

    .game-container {
        display: block;
        padding: 5px;
        height: 100vh;
        overflow: hidden;
    }

    .game-header {
        padding: 4px 8px;
        margin-bottom: 4px;
    }

    .game-header h1 {
        font-size: 1.2rem;
        margin: 0;
    }

    .game-stats {
        gap: 8px;
    }

    .game-stats span {
        font-size: 0.75rem;
        padding: 3px 6px;
    }

    .header-right {
        gap: 4px;
    }

    .btn {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    #fullscreenBtn {
        font-size: 0.8rem;
        padding: 4px 6px;
        min-width: 28px;
    }

    .game-board {
        gap: 4px;
        height: calc(100vh - 80px);
        padding: 2px;
    }

    .foundation-area {
        gap: 2px;
        margin-bottom: 4px;
    }

    .stock-area {
        gap: 4px;
        margin-bottom: 4px;
    }

    .tableau-area {
        gap: 2px;
        flex: 1;
        min-height: 0;
    }

    /* Smaller cards for landscape mobile */
    .card,
    .foundation-pile,
    .stock-pile,
    .tableau-pile {
        width: calc((100vw - 30px) / 12);
        height: calc((100vw - 30px) / 12 * 1.4);
        max-width: 55px;
        max-height: 77px;
        min-width: 40px;
        min-height: 56px;
    }

    .card {
        width: calc((100vw - 30px) / 12 - 2px);
        height: calc((100vw - 30px) / 12 * 1.4 - 2px);
        max-width: 53px;
        max-height: 75px;
        min-width: 38px;
        min-height: 54px;
        font-size: calc(0.5rem + 0.2vw);
    }

    .card-center {
        font-size: calc(1rem + 0.3vw);
    }
}
