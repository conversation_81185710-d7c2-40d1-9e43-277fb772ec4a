class SpiderSolitaire {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []];
        this.completed = [];
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];
        this.difficulty = 'easy';

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = this.isMobile() ? 12 : 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        this.suits = ['spades', 'hearts', 'clubs', 'diamonds'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.isAnimatingCard = false;
        this.isAutoCompleting = false;
        this.isProcessingAction = false;
        this.lastActionTime = 0;
        this.actionCooldown = 100;
        this.buttonCooldowns = new Map();

        this.initializeGame();
        if (!testMode) {
            this.bindEvents();
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }


    async animateCardMovement(cards, startPositions, endPositions, options = {}) {
        const {
            speedMultiplier = 1,
            animationType = 'flying-to-tableau',
            staggerDelay = 60,
            baseSpeed = 800
        } = options;

        if (!Array.isArray(cards)) {
            cards = [cards];
            startPositions = [startPositions];
            endPositions = [endPositions];
        }

        const animations = cards.map((card, index) => {
            const $card = card instanceof jQuery ? card : $(card);
            if (!$card.length) return Promise.resolve();

            const startPos = startPositions[index] || startPositions[0];
            const endPos = endPositions[index] || endPositions[0];

            const distance = Math.sqrt(
                Math.pow(endPos.x - startPos.x, 2) +
                Math.pow(endPos.y - startPos.y, 2)
            );
            const duration = Math.max(200, (distance / baseSpeed) * 1000 / speedMultiplier);

            return new Promise((resolve) => {
                setTimeout(() => {
                    this.animateSingleCard($card, startPos, endPos, animationType, duration)
                        .then(resolve);
                }, index * staggerDelay);
            });
        });

        return Promise.all(animations);
    }

    async animateSingleCard($card, startPos, endPos, animationType, duration) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            const originalStyles = {
                position: $card.css('position'),
                left: $card.css('left'),
                top: $card.css('top'),
                zIndex: $card.css('z-index'),
                transition: $card.css('transition'),
                transform: $card.css('transform')
            };

            const cardWidth = $card.outerWidth();
            const cardHeight = $card.outerHeight();

            $card.css({
                position: 'fixed',
                left: (startPos.x - cardWidth / 2) + 'px',
                top: (startPos.y - cardHeight / 2) + 'px',
                zIndex: 10000,
                transition: 'none',
                transform: 'translateZ(0)'
            });

            $card.addClass(animationType);
            $card[0].offsetHeight;
            $card.css({
                left: (endPos.x - cardWidth / 2) + 'px',
                top: (endPos.y - cardHeight / 2) + 'px',
                transition: `all ${duration}ms linear`
            });

            setTimeout(() => {
                $card.removeClass(animationType);
                Object.keys(originalStyles).forEach(prop => {
                    $card.css(prop, originalStyles[prop]);
                });
                resolve();
            }, duration);
        });
    }

    // 专门的收牌动画方法
    async animateCardToCompleted($card, targetPos, duration) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            // 获取卡牌当前位置和目标位置的差值
            const currentRect = $card[0].getBoundingClientRect();
            const currentPos = {
                x: currentRect.left + currentRect.width / 2,
                y: currentRect.top + currentRect.height / 2
            };

            const deltaX = targetPos.x - currentPos.x;
            const deltaY = targetPos.y - currentPos.y;

            // 保存原始样式
            const originalTransform = $card.css('transform');
            const originalTransition = $card.css('transition');
            const originalZIndex = $card.css('z-index');

            // 设置高z-index
            $card.css({
                'z-index': 10000,
                'transition': 'none'
            });

            // 强制重绘
            $card[0].offsetHeight;

            // 开始动画（只移动，不缩放不透明）
            $card.css({
                'transition': `transform ${duration}ms ease-out`,
                'transform': `${originalTransform} translate(${deltaX}px, ${deltaY}px)`
            });

            setTimeout(() => {
                // 恢复原始样式
                $card.css({
                    'transform': originalTransform,
                    'transition': originalTransition,
                    'z-index': originalZIndex
                });
                resolve();
            }, duration);
        });
    }

    async animateDealFromStock() {
        const $cardBack = $('#stock .card-back');
        if (!$cardBack.length) return;

        const cardsToAdd = [];
        const animations = [];


        for (let i = 0; i < 10 && this.stock.length > 0; i++) {
            const card = this.stock.pop();
            card.faceUp = true;
            cardsToAdd.push({ card, targetPile: i });
        }


        for (let i = 0; i < cardsToAdd.length; i++) {
            const { card, targetPile } = cardsToAdd[i];

            animations.push(new Promise(resolve => {
                setTimeout(async () => {

                    const cardBackRect = $cardBack[0].getBoundingClientRect();


                    const $tempCard = this.createCardElement(card);
                    $tempCard.css({
                        position: 'fixed',
                        left: cardBackRect.left + 'px',
                        top: cardBackRect.top + 'px',
                        width: cardBackRect.width + 'px',
                        height: cardBackRect.height + 'px',
                        zIndex: 10001 + i,
                        transform: 'scale(1.05)',
                        transition: 'none'
                    });
                    $('body').append($tempCard);


                    const startPos = {
                        x: cardBackRect.left + cardBackRect.width / 2,
                        y: cardBackRect.top + cardBackRect.height / 2
                    };
                    const endPos = this.getTableauPileTargetPosition(targetPile);


                    await this.animateSingleCard($tempCard, startPos, endPos, 'flying-from-stock', 450);


                    this.tableau[targetPile].push(card);
                    $tempCard.remove();


                    this.updateSingleTableauPile(targetPile);

                    resolve();
                }, i * 60);
            }));
        }


        await Promise.all(animations);


        this.updateStock();
    }

    async animateCompletedSequence(pileIndex) {
        console.log('开始收牌动画，pile:', pileIndex);
        const $pile = $(`#tableau-${pileIndex}`);
        const $cards = $pile.find('.card').slice(-13);
        console.log('找到卡牌数量:', $cards.length);

        // 计算实际的牌背位置（与addCompletedSequenceDisplay中的逻辑一致）
        const $completedContainer = $('#completedSequences');
        const containerRect = $completedContainer[0].getBoundingClientRect();
        const completedCount = this.completed.length + 1; // +1因为即将添加新的完成序列
        const leftOffset = (completedCount - 1) * 15; // 15px overlap for each card

        const targetPos = {
            x: containerRect.left + leftOffset + 50, // 50px是卡牌宽度的一半
            y: containerRect.top + 72.5 // 72.5px是卡牌高度的一半
        };
        console.log('目标位置:', targetPos);

        // 按A到K顺序（倒序）启动重叠式动画
        const animations = [];
        let firstCardCompleted = false;

        for (let i = $cards.length - 1; i >= 0; i--) {
            const $card = $($cards[i]);
            const cardIndex = $cards.length - 1 - i; // A=0, 2=1, ..., K=12

            animations.push(new Promise((resolve) => {
                setTimeout(async () => {
                    console.log(`开始动画卡牌 ${cardIndex} -> 目标:`, targetPos);

                    // 执行专门的收牌动画
                    await this.animateCardToCompleted($card, targetPos, 600);

                    console.log(`卡牌 ${cardIndex} 动画完成，移除卡牌`);
                    // 动画完成后立即移除卡牌
                    $card.remove();

                    // 第一张卡牌完成时就显示完成区域的牌背
                    if (!firstCardCompleted) {
                        firstCardCompleted = true;
                        this.addCompletedSequenceDisplay();
                    }

                    resolve();
                }, cardIndex * 100); // 每张卡牌间隔100ms开始
            }));
        }

        // 等待所有动画完成
        await Promise.all(animations);

        // 动画完成后更新tableau显示
        this.updateSingleTableauPile(pileIndex);
    }

    addCompletedSequenceDisplay() {
        const $completedContainer = $('#completedSequences');

        // Hide placeholder if this is the first completed sequence
        if (this.completed.length === 1) {
            $completedContainer.find('.completed-placeholder').hide();
        }

        const completedCount = this.completed.length;
        const leftOffset = (completedCount - 1) * 15; // 15px overlap for each card

        const $cardBack = $('<div>').addClass('card face-down completed-sequence-display');
        $cardBack.css({
            width: '100px',
            height: '145px',
            position: 'absolute',
            left: leftOffset + 'px',
            top: '0px',
            zIndex: completedCount
        });

        $completedContainer.append($cardBack);
    }



    getCardPosition($card) {
        const offset = $card.offset();
        const width = $card.outerWidth();
        const height = $card.outerHeight();
        
        return {
            x: offset.left + width / 2,
            y: offset.top + height / 2
        };
    }

    getTableauPilePosition(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const offset = $pile.offset();
        const height = $pile.outerHeight();
        const width = $pile.outerWidth();

        // Get the last card in the pile to position the flying card correctly
        const $lastCard = $pile.find('.card').last();
        let targetY = offset.top + height - 20; // Default position

        if ($lastCard.length) {
            const lastCardOffset = $lastCard.offset();
            const lastCardHeight = $lastCard.outerHeight();
            targetY = lastCardOffset.top + lastCardHeight + 8; // Position below the last card with small gap
        }

        return {
            x: offset.left + width / 2,
            y: targetY
        };
    }

    getTableauPileTargetPosition(pileIndex) {
        const $pile = $(`#tableau-${pileIndex}`);
        const $lastCard = $pile.find('.card').last();
        const offset = window.innerWidth < 1024 ? 15 : 25;

        if ($lastCard.length) {

            const lastCardRect = $lastCard[0].getBoundingClientRect();
            return {
                x: lastCardRect.left + lastCardRect.width / 2,
                y: lastCardRect.top + offset + lastCardRect.height / 2
            };
        } else {

            const pileRect = $pile[0].getBoundingClientRect();
            return {
                x: pileRect.left + pileRect.width / 2,
                y: pileRect.top + pileRect.height / 2
            };
        }
    }

    getCompletedAreaPosition() {
        const $placeholder = $('.completed-placeholder');
        const $completedArea = $('.completed-sequences');
        const $target = $placeholder.length ? $placeholder : $completedArea;
        const offset = $target.offset();

        if (!offset) {
            return {
                x: window.innerWidth - 100,
                y: 100
            };
        }

        return {
            x: offset.left + $target.outerWidth() / 2,
            y: offset.top + $target.outerHeight() / 2
        };
    }


    setDifficulty(difficulty) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        this.difficulty = difficulty;

        // Update difficulty button states
        $('.btn-difficulty').removeClass('active');
        $(`#${difficulty}Btn`).addClass('active');

        this.newGame();
    }

    initializeGame() {
        this.deck = this.createDeck();
        this.shuffleArray(this.deck);
        this.dealCards();
        this.updateDisplay();
        this.startTimer();
    }

    createDeck() {
        const suits = this.getSuitsForDifficulty();
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        let idCounter = 1;
        const deck = [];

        // Calculate how many copies of each suit we need to get 104 cards total
        const copiesPerSuit = 104 / (suits.length * ranks.length);

        for (let suit of suits) {
            for (let i = 0; i < copiesPerSuit; i++) {
                for (let rank of ranks) {
                    deck.push({
                        id: idCounter++,
                        suit,
                        rank,
                        value: this.getCardValue(rank),
                        color: this.suitColors[suit],
                        faceUp: false
                    });
                }
            }
        }
        return deck;
    }

    getSuitsForDifficulty() {
        switch (this.difficulty) {
            case 'easy':
                return ['spades']; // Only spades, but we'll create 8 copies to fill 104 cards
            case 'medium':
                return ['spades', 'hearts']; // 2 suits, 4 copies each
            case 'hard':
                return ['spades', 'hearts', 'clubs', 'diamonds']; // All 4 suits, 2 copies each
            default:
                return ['spades'];
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    dealCards() {
        this.stock = [];
        this.tableau = [[], [], [], [], [], [], [], [], [], []];
        this.completed = [];

        // Deal cards to tableau: first 4 piles get 6 cards, last 6 piles get 5 cards
        let cardIndex = 0;
        
        // First 4 piles: 6 cards each (5 face down, 1 face up)
        for (let pile = 0; pile < 4; pile++) {
            for (let card = 0; card < 6; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 5); // Last card face up
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        // Last 6 piles: 5 cards each (4 face down, 1 face up)
        for (let pile = 4; pile < 10; pile++) {
            for (let card = 0; card < 5; card++) {
                if (cardIndex < this.deck.length) {
                    const currentCard = this.deck[cardIndex];
                    currentCard.faceUp = (card === 4); // Last card face up
                    this.tableau[pile].push(currentCard);
                    cardIndex++;
                }
            }
        }

        // Remaining cards go to stock
        while (cardIndex < this.deck.length) {
            this.stock.push(this.deck[cardIndex]);
            cardIndex++;
        }
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }


    calculateCardStates(pileIndex) {
        const pile = this.tableau[pileIndex];
        const states = {
            movableCards: [],
            disabledCards: [],
            topCardIndex: pile.length - 1
        };

        if (pile.length === 0) return states;


        states.movableCards = this.getMovableCards(pileIndex, states.topCardIndex);


        const faceUpCards = pile.filter(card => card.faceUp);
        states.disabledCards = faceUpCards.filter(card =>
            !states.movableCards.some(movableCard => movableCard.id === card.id)
        );

        return states;
    }

    // Check if cards can be moved together (same suit, descending order)
    canMoveCardsTogether(cards) {
        if (cards.length <= 1) return true;

        for (let i = 0; i < cards.length - 1; i++) {
            const currentCard = cards[i];
            const nextCard = cards[i + 1];

            // Must be same suit and descending order
            if (currentCard.suit !== nextCard.suit ||
                currentCard.value !== nextCard.value + 1) {
                return false;
            }
        }
        return true;
    }

    // Get movable cards from a tableau pile starting from a specific index
    getMovableCards(pileIndex, startIndex) {
        const pile = this.tableau[pileIndex];
        if (startIndex >= pile.length || startIndex < 0) return [];


        const movableCards = [];
        const startCard = pile[startIndex];

        if (!startCard || !startCard.faceUp) return [];

        movableCards.push(startCard);


        for (let i = startIndex - 1; i >= 0; i--) {
            const currentCard = pile[i];   // 下面的牌

            if (!currentCard.faceUp) break;


            const lastCard = movableCards[movableCards.length - 1];

            if (lastCard.rank === currentCard.rank && lastCard.suit === currentCard.suit) {
                break;
            }

            if (lastCard.suit === currentCard.suit &&
                currentCard.value === lastCard.value + 1) {
                movableCards.push(currentCard);
            } else {
                break;
            }
        }

        return movableCards.reverse();
    }

    canPlaceCard(cardToPlace, targetCard) {
        if (!targetCard) return true;

        return cardToPlace.value === targetCard.value - 1;
    }

    // Check for completed sequences (K to A of same suit)
    async checkForCompletedSequences() {
        let foundCompleted = false;
        let hasMoreSequences = true;

        // Keep checking until no more complete sequences are found
        while (hasMoreSequences) {
            hasMoreSequences = false;

            for (let pileIndex = 0; pileIndex < this.tableau.length; pileIndex++) {
                const pile = this.tableau[pileIndex];
                if (pile.length < 13) continue;

                // Check if top 13 cards form a complete sequence K to A
                const topCards = pile.slice(-13);
                if (this.isCompleteSequence(topCards)) {
                    // Execute animation first (before removing from data)
                    await this.animateCompletedSequence(pileIndex);

                    // Remove the completed sequence from the tableau
                    this.tableau[pileIndex].splice(-13, 13);

                    // Add to completed sequences
                    this.completed.push(topCards);

                    this.score += 100; // Bonus for completing sequence
                    foundCompleted = true;
                    hasMoreSequences = true; // Found one, check again

                    // Flip the new top card if it exists and is face down
                    if (this.tableau[pileIndex].length > 0) {
                        const newTopCard = this.tableau[pileIndex][this.tableau[pileIndex].length - 1];
                        if (newTopCard && !newTopCard.faceUp) {
                            newTopCard.faceUp = true;
                            this.score += 5;
                        }
                    }

                    // Update display
                    this.updateDisplay();

                    // Break out of the for loop to restart checking from the beginning
                    break;
                }
            }
        }

        return foundCompleted;
    }

    isCompleteSequence(cards) {
        if (cards.length !== 13) return false;
        
        const suit = cards[0].suit;
        
        // Check if all cards are same suit and in descending order K to A
        for (let i = 0; i < 13; i++) {
            const expectedValue = 13 - i; // K=13, Q=12, ..., A=1
            if (cards[i].suit !== suit || cards[i].value !== expectedValue || !cards[i].faceUp) {
                return false;
            }
        }
        
        return true;
    }

    // Deal new cards from stock (one to each non-empty pile)
    async dealFromStock() {
        if (this.stock.length === 0) {
            return false;
        }
        if (this.isAnimatingCard) {
            return false;
        }

        for (let i = 0; i < this.tableau.length; i++) {
            if (this.tableau[i].length === 0) {
                if (typeof gameAlert === 'function') {
                    gameAlert('All piles must have at least one card before dealing new cards!', 'Cannot Deal');
                } else {
                    alert('All piles must have at least one card before dealing new cards!');
                }
                return false;
            }
        }

        this.isAnimatingCard = true;
        try {
            await this.animateDealFromStock();
        } finally {
            this.isAnimatingCard = false;
        }

        this.moves++;
        this.recordMove({
            type: 'deal',
            count: Math.min(10, this.stock.length)
        });

        return true;
    }

    checkWinCondition() {
        // Win when all 8 sequences are completed (8 suits × 13 cards = 104 cards)
        const expectedSequences = this.difficulty === 'easy' ? 8 : 
                                 this.difficulty === 'medium' ? 8 : 8;
        return this.completed.length >= expectedSequences;
    }

    recordMove(move) {
        this.moveHistory.push(move);
    }

    canPerformAction(actionType = 'default') {
        const now = Date.now();
        if (this.isAnimatingCard || this.isProcessingAction) {
            return false;
        }
        if (now - this.lastActionTime < this.actionCooldown) {
            return false;
        }

        const lastButtonTime = this.buttonCooldowns.get(actionType) || 0;
        if (now - lastButtonTime < 300) {
            return false;
        }

        return true;
    }

    setActionCooldown(actionType = 'default') {
        this.lastActionTime = Date.now();
        this.buttonCooldowns.set(actionType, Date.now());
    }

    bindEvents() {
        $('#homeBtn').on('click', () => {
            window.location.href = '/';
        });

        $('#newGameBtn').on('click', () => {
            if (!this.canPerformAction('newGame')) return;
            this.setActionCooldown('newGame');

            if (!this.hasAutoFullscreened) {
                this.hasAutoFullscreened = true;
                this.autoFullscreen();
            }
            this.newGame();
        });

        $('#undoBtn').on('click', () => {
            if (!this.canPerformAction('undo')) return;
            this.setActionCooldown('undo');
            this.undoMove();
        });

        $('#hintBtn').on('click', () => {
            if (!this.canPerformAction('hint')) return;
            this.setActionCooldown('hint');
            this.showHint();
        });

        $('#helpBtn').on('click', () => {
            if (!this.canPerformAction('help')) return;
            this.setActionCooldown('help');
            this.showHelp();
        });

        // Mobile stock button
        $('#stockBtn').on('click', async () => {
            if (!this.canPerformAction('dealStock')) return;
            this.setActionCooldown('dealStock');
            this.isProcessingAction = true;

            try {
                await this.dealFromStock();
                await this.checkForCompletedSequences();
                this.checkWinCondition();
            } finally {
                this.isProcessingAction = false;
            }
        });

        $('#closeHelpBtn').on('click', () => this.hideHelp());
        $('#closeHelpBtnBottom').on('click', () => this.hideHelp());

        $('#fullscreenBtn').on('click', () => {
            if (!this.canPerformAction('fullscreen')) return;
            this.setActionCooldown('fullscreen');
            this.toggleFullscreen();
        });

        // Add difficulty selection buttons
        $('#easyBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('easy');
        });

        $('#mediumBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('medium');
        });

        $('#hardBtn').on('click', () => {
            if (!this.canPerformAction('difficulty')) return;
            this.setActionCooldown('difficulty');
            this.setDifficulty('hard');
        });

        // Add message button handlers
        $('#playAgainBtn').on('click', () => {
            if (!this.canPerformAction('playAgain')) return;
            this.setActionCooldown('playAgain');
            this.hideMessage();
            this.newGame();
        });

        $('#closeMessageBtn').on('click', () => this.hideMessage());

        // Mouse and touch events
        $(document).on('mousedown', async (e) => await this.onPointerDown(e.originalEvent || e));
        $(document).on('mousemove', (e) => this.onPointerMove(e.originalEvent || e));
        $(document).on('mouseup', async (e) => await this.onPointerUp(e.originalEvent || e));

        document.addEventListener('touchstart', async (e) => await this.onPointerDown(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.onPointerMove(e), { passive: false });
        document.addEventListener('touchend', async (e) => await this.onPointerUp(e), { passive: false });
        document.addEventListener('touchcancel', async (e) => await this.onPointerUp(e), { passive: false });

        $(document).on('dragstart', (e) => e.preventDefault());
        $(document).on('selectstart', (e) => e.preventDefault());
        $(document).on('keydown', (e) => this.onKeyDown(e));
        $(document).on('contextmenu', (e) => e.preventDefault());

        // Fullscreen events
        $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', () => {
            this.updateFullscreenButton();
        });

        this.updateFullscreenButton();
    }

    newGame() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        this.isProcessingAction = true;
        this.stopTimer();
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.completed = [];
        this.resetDragState();

        $('.card').removeClass('dragging moving-to-foundation moving-to-tableau card-returning');
        $('.card').css({
            left: '',
            top: '',
            position: '',
            'z-index': '',
            'transition': ''
        });

        // Clear completed sequences display and show placeholder
        $('.completed-sequence-display').remove();
        $('#completedSequences .completed-placeholder').show();

        this.hideMessage();
        this.hideHelp();
        this.initializeGame();

        // Initialize mobile stock button
        this.updateMobileStockButton();
        this.isProcessingAction = false;
    }

    updateMobileStockButton() {
        const mobileStockCount = $('#stockCount');
        const mobileStockBtn = $('#stockBtn');

        if (this.stock && this.stock.length > 0) {
            mobileStockCount.text(this.stock.length);
            mobileStockBtn.prop('disabled', false);
            mobileStockBtn.attr('title', `点击发牌 (剩余${this.stock.length}张)`);
        } else {
            mobileStockCount.text('0');
            mobileStockBtn.prop('disabled', true);
            mobileStockBtn.attr('title', '无剩余卡牌');
        }
    }

    resetDragState() {
        this.isDragging = false;
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.clickedCard = null;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.isAnimatingCard = false;

        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    startTimer() {
        this.startTime = Date.now();
        this.timer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            $('#timer').text(`${minutes}:${seconds}`);
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    formatTime() {
        if (!this.startTime) return '00:00';
        
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    updateDisplay() {
        if (!this.testMode) {
            this.updateStock();
            this.updateTableau();
            this.updateStats();
            this.updateCompleted();
        }

        // Check win condition
        if (this.checkWinCondition()) {
            this.gameWon = true;
            this.showWinMessage();
        }
    }

    updateStock() {
        const stockElement = $('#stock');
        const mobileStockCount = $('#stockCount');
        const mobileStockBtn = $('#stockBtn');

        stockElement.empty();

        if (this.stock.length > 0) {
            // Desktop stock display
            const cardBack = $('<div class="card-back"></div>');
            stockElement.append(cardBack);

            const countIndicator = $('<div class="stock-count"></div>');
            countIndicator.text(this.stock.length);
            countIndicator.css({
                position: 'absolute',
                top: '5px',
                right: '5px',
                background: 'rgba(0,0,0,0.7)',
                color: 'white',
                borderRadius: '10px',
                padding: '2px 6px',
                fontSize: '12px',
                fontWeight: 'bold',
                zIndex: 10
            });
            stockElement.append(countIndicator);

            stockElement.removeClass('empty');

            // Mobile stock button update
            mobileStockCount.text(this.stock.length);
            mobileStockBtn.prop('disabled', false);
            mobileStockBtn.attr('title', `Click to Deal 10 Cards (${this.stock.length} remaining)`);
        } else {
            stockElement.html('<div class="stock-empty">Empty</div>');
            stockElement.addClass('empty');

            // Mobile stock button update
            mobileStockCount.text('0');
            mobileStockBtn.prop('disabled', true);
            mobileStockBtn.attr('title', 'No cards remaining');
        }
    }

    updateTableau() {
        for (let i = 0; i < 10; i++) {
            this.updateSingleTableauPile(i);
        }
    }

    updateSingleTableauPile(pileIndex) {
        const tableauElement = $(`#tableau-${pileIndex}`);
        tableauElement.empty();

        const spacing = window.innerWidth < 1024 ? 15 : 25;
        const cardStates = this.calculateCardStates(pileIndex);

        this.tableau[pileIndex].forEach((card, index) => {
            const cardElement = this.createCardElement(card);
            cardElement.css({
                position: 'absolute',
                top: `${index * spacing}px`,
                zIndex: index + 1
            });

            if (card.faceUp) {
                this.applyCardState(cardElement, card, cardStates);
            }

            if (card.faceUp && index === this.tableau[pileIndex].length - 1) {
                cardElement.addClass('top-card');
            }

            tableauElement.append(cardElement);
        });
    }

    applyCardState(cardElement, card, cardStates) {
        cardElement.removeClass('draggable-card disabled-card');
        cardElement.css({
            'cursor': '',
            'box-shadow': ''
        });

        const isMovable = cardStates.movableCards.some(movableCard => movableCard.id === card.id);

        if (isMovable) {
            cardElement.addClass('draggable-card');
        } else {
            cardElement.addClass('disabled-card');
        }
    }

    updateCompleted() {
        // Update completed sequences display
        const completedElement = $('#completed');
        if (completedElement.length) {
            completedElement.text(`Completed: ${this.completed.length}/8`);
        }
    }

    createCardElement(card) {
        const cardElement = $('<div>').addClass('card');

        if (!card.faceUp) {
            cardElement.addClass('face-down');
            return cardElement;
        }

        cardElement.addClass(card.color);
        cardElement.attr('data-suit', card.suit);
        cardElement.attr('data-rank', card.rank);
        cardElement.attr('data-value', card.value);

        const symbol = this.suitSymbols[card.suit];

        cardElement.html(`
            <div class="card-top">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
            <div class="card-center">${symbol}</div>
            <div class="card-bottom">
                <span class="rank">${card.rank}</span>
                <span class="suit">${symbol}</span>
            </div>
        `);

        return cardElement;
    }

    updateStats() {
        const $score = $('#score');
        const $moves = $('#moves');
        const $completed = $('#completed');
        
        // Animate score update
        if ($score.text() !== this.score.toString()) {
            $score.addClass('score-updating');
            $score.text(this.score);
            setTimeout(() => $score.removeClass('score-updating'), 500);
        }
        
        // Animate moves update
        if ($moves.text() !== this.moves.toString()) {
            $moves.addClass('moves-updating');
            $moves.text(this.moves);
            setTimeout(() => $moves.removeClass('moves-updating'), 300);
        }
        
        // Animate completed update
        if ($completed.text() !== `${this.completed.length}/8`) {
            $completed.addClass('completed-updating');
            $completed.text(`${this.completed.length}/8`);
            setTimeout(() => $completed.removeClass('completed-updating'), 600);
        }
        
        $('#timer').text(this.formatTime());
    }

    // Placeholder methods for compatibility
    async onPointerDown(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        if (e.type === 'mousedown' && (e.which !== 1 && e.button !== 0)) return;

        this.resetDragState();
        const coords = this.getEventCoordinates(e);
        this.dragStartPos = { x: coords.clientX, y: coords.clientY };

        const target = e.target || e.srcElement;

        if (target.closest('#stock') || $(target).closest('#stock').length) {
            e.preventDefault();
            e.stopPropagation();

            if (!this.canPerformAction('dealStock')) return;
            this.setActionCooldown('dealStock');
            this.isProcessingAction = true;

            try {
                await this.dealFromStock();
                await this.checkForCompletedSequences();
                this.checkWinCondition();
            } finally {
                this.isProcessingAction = false;
            }
            return;
        }

        if (!this.canPerformAction('pointer')) return;

        const cardElement = target.closest('.card') || $(target).closest('.card')[0];
        if (!cardElement) return;

        e.preventDefault();
        e.stopPropagation();

        const $cardElement = $(cardElement);

        if ($cardElement.hasClass('face-down')) {
            return;
        }


        if ($cardElement.hasClass('disabled-card')) {
            return;
        }

        this.setActionCooldown('pointer');

        const cardRect = cardElement.getBoundingClientRect();
        this.dragOffset.x = coords.clientX - cardRect.left;
        this.dragOffset.y = coords.clientY - cardRect.top;

        this.prepareDragData($cardElement);


        if (!this.draggedCards || this.draggedCards.length === 0) {
            this.resetDragState();
            return;
        }

        const isMobile = e.type.startsWith('touch');
        if (isMobile) {
            this.longPressTimer = setTimeout(() => {
                if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
                    navigator.vibrate && navigator.vibrate(50);
                }
            }, this.longPressDelay);

            // Prevent scrolling on mobile during card interaction
            e.preventDefault();
        }
    }

    onPointerMove(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        if (!this.draggedCards || this.draggedCards.length === 0) return;

        const coords = this.getEventCoordinates(e);
        const deltaX = coords.clientX - this.dragStartPos.x;
        const deltaY = coords.clientY - this.dragStartPos.y;

        if (!this.isDragging) {
            if (Math.abs(deltaX) > this.dragThreshold || Math.abs(deltaY) > this.dragThreshold) {
                this.startDrag();
            }
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        this.updateDragPosition(coords.clientX, coords.clientY);
        this.updateDropZones(coords.clientX, coords.clientY);
    }

    async onPointerUp(e) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }

        if (this.isProcessingAction) return;

        if (!this.isDragging && this.draggedCards && this.draggedCards.length > 0) {
            this.isProcessingAction = true;
            try {
                await this.handleCardClick();
            } finally {
                this.isProcessingAction = false;
                this.resetDragState();
            }
            return;
        }

        if (this.isDragging) {
            this.isProcessingAction = true;
            try {
                await this.handleDrop(e);
            } finally {
                this.isProcessingAction = false;
            }
        }

        this.resetDragState();
    }

    getEventCoordinates(e) {
        if (e.type && e.type.startsWith('touch')) {
            const touchEvent = e.originalEvent || e;
            if (touchEvent.touches && touchEvent.touches.length > 0) {
                const touch = touchEvent.touches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            } else if (touchEvent.changedTouches && touchEvent.changedTouches.length > 0) {
                const touch = touchEvent.changedTouches[0];
                return {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
            }
        }
        return {
            clientX: e.clientX || 0,
            clientY: e.clientY || 0
        };
    }

    prepareDragData($cardElement) {
        const pileElement = $cardElement.closest('.tableau-pile');
        if (!pileElement.length) return;

        const pileId = pileElement.attr('id');
        const pileIndex = parseInt(pileId.split('-')[1]);


        const cards = pileElement.find('.card');
        let cardIndex = -1;
        for (let i = 0; i < cards.length; i++) {
            if (cards[i] === $cardElement[0]) {
                cardIndex = i;
                break;
            }
        }

        if (cardIndex === -1) return;

        this.draggedFrom = { type: 'tableau', index: pileIndex };


        if (this.tableau[pileIndex].length === 0 || cardIndex >= this.tableau[pileIndex].length) {
            this.draggedCards = [];
            return;
        }


        const clickedCard = this.tableau[pileIndex][cardIndex];
        if (!clickedCard) {
            this.draggedCards = [];
            return;
        }
        this.clickedCard = clickedCard;


        const topCardIndex = this.tableau[pileIndex].length - 1;
        this.draggedCards = this.getMovableCards(pileIndex, topCardIndex);


        const clickedCardIndex = this.draggedCards.findIndex(card => card.id === clickedCard.id);
        if (clickedCardIndex !== -1) {
            this.draggedCards = this.draggedCards.slice(clickedCardIndex);
        } else {
            this.draggedCards = [];
        }

        const isClickable = this.draggedCards.some(movableCard => movableCard.id === clickedCard.id);
        if (!isClickable) {
            this.draggedCards = [];
            return;
        }
        if (this.draggedCards.length === 0) return;

        this.originalCardPositions = [];

        const sourcePile = this.tableau[pileIndex];
        const startIndex = sourcePile.length - this.draggedCards.length;

        const allCardElements = pileElement.find('.card');
        for (let j = 0; j < this.draggedCards.length; j++) {
            const domIndex = startIndex + j;
            if (domIndex >= 0 && domIndex < allCardElements.length) {
                const card = allCardElements[domIndex];
                const $card = $(card);
                const rect = card.getBoundingClientRect();
                this.originalCardPositions.push({
                    element: $card,
                    left: rect.left,
                    top: rect.top,
                    position: $card.css('position'),
                    zIndex: $card.css('z-index')
                });
            }
        }
    }

    startDrag() {
        this.isDragging = true;

        this.draggedElements = [];
        
        this.originalCardPositions.forEach((pos, index) => {
            const $card = pos.element;
            const rect = $card[0].getBoundingClientRect();

            $card.css({
                position: 'fixed',
                left: rect.left + 'px',
                top: rect.top + 'px',
                zIndex: index + 9999,
                pointerEvents: 'none',
                transition: 'none',
                transform: 'none'
            });
            this.draggedElements.push($card);
        });
    }

    updateDragPosition(clientX, clientY) {
        if (!this.draggedElements || this.draggedElements.length === 0) return;

        requestAnimationFrame(() => {
            if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;

            const baseX = clientX - this.dragOffset.x;
            const baseY = clientY - this.dragOffset.y;
            const spacing = window.innerWidth < 768 ? 8 : (window.innerWidth < 1024 ? 15 : 25);
            const scale = this.isMobile() ? 1.05 : 1.02;

            this.draggedElements.forEach((element, index) => {
                element.css({
                    left: baseX + 'px',
                    top: (baseY + index * spacing) + 'px',
                    transform: `scale(${scale})`,
                    opacity: 0.95,
                    zIndex: 1000 + index
                });
            });
        });
    }

    updateDropZones(clientX, clientY) {
        $('.drop-zone-valid, .drop-zone-invalid').removeClass('drop-zone-valid drop-zone-invalid');

        const elementBelow = document.elementFromPoint(clientX, clientY);
        if (!elementBelow) return;

        const $pile = $(elementBelow).closest('.tableau-pile');
        if (!$pile.length) return;
    }

    async handleDrop(e) {
        const coords = this.getEventCoordinates(e);
        const elementBelow = document.elementFromPoint(coords.clientX, coords.clientY);
        
        if (!elementBelow) {
            this.returnCardsToOriginalPosition();
            return;
        }

        const $targetPile = $(elementBelow).closest('.tableau-pile');
        if (!$targetPile.length) {
            this.returnCardsToOriginalPosition();
            return;
        }

        const targetPileIndex = parseInt($targetPile.attr('id').split('-')[1]);

        if (this.isValidMove(targetPileIndex)) {
            await this.executeMove(targetPileIndex);
            await this.checkForCompletedSequences();
            this.checkWinCondition();
        } else {
            this.returnCardsToOriginalPosition();
        }
    }

    async handleCardClick() {
        const validMoves = this.findValidMoves();

        if (validMoves.length === 0) {
            return;
        }

        const bestMove = this.selectBestMove(validMoves);

        await this.executeAutoMove(bestMove.targetPile);
    }

    findValidMoves() {
        const validMoves = [];

        for (let i = 0; i < 10; i++) {
            if (i !== this.draggedFrom.index && this.isValidMove(i)) {
                const targetPile = this.tableau[i];
                validMoves.push({
                    targetPile: i,
                    pileLength: targetPile.length,
                    isEmpty: targetPile.length === 0
                });
            }
        }

        return validMoves;
    }

    selectBestMove(validMoves) {
        const nonEmptyMoves = validMoves.filter(move => !move.isEmpty);
        const emptyMoves = validMoves.filter(move => move.isEmpty);

        if (nonEmptyMoves.length > 0) {
            // Calculate the longest continuous sequence for each valid move
            const movesWithSequenceLength = nonEmptyMoves.map(move => {
                const targetPile = this.tableau[move.targetPile];
                const movingCard = this.draggedCards[0];

                // Calculate how long the continuous sequence would be after this move
                let sequenceLength = this.draggedCards.length;

                // Check backwards from the target pile's top card
                for (let i = targetPile.length - 1; i >= 0; i--) {
                    const card = targetPile[i];
                    if (!card.faceUp) break;

                    // Check if this card continues the sequence
                    const expectedValue = movingCard.value + (sequenceLength);
                    if (card.value === expectedValue && card.suit === movingCard.suit) {
                        sequenceLength++;
                    } else {
                        break;
                    }
                }

                return {
                    ...move,
                    sequenceLength: sequenceLength
                };
            });

            // Return the move that creates the longest continuous sequence
            return movesWithSequenceLength.reduce((best, current) =>
                current.sequenceLength > best.sequenceLength ? current : best
            );
        }

        if (emptyMoves.length > 0) {
            return emptyMoves[0];
        }

        return validMoves[0];
    }

    async executeAutoMove(targetPileIndex) {
        const pileIndex = this.draggedFrom.index;
        const cards = this.draggedCards;
        const fromElements = [];


        const $pileElement = $(`#tableau-${pileIndex}`);
        const allCardElements = $pileElement.find('.card');


        const sourcePile = this.tableau[pileIndex];
        const startIndex = sourcePile.length - cards.length;


        for (let j = 0; j < cards.length; j++) {
            const domIndex = startIndex + j;
            if (domIndex >= 0 && domIndex < allCardElements.length) {
                const $el = $(allCardElements[domIndex]);
                fromElements.push($el);
            }
        }

        if (fromElements.length === cards.length) {
            await this.animateAutoMatchFlight(cards, fromElements, targetPileIndex);
            await this.executeMove(targetPileIndex);
            await this.checkForCompletedSequences();
            this.checkWinCondition();
        }
    }

    async animateAutoMatchFlight(cards, fromElements, toPileIndex) {
        this.isAnimatingCard = true;


        fromElements.forEach($el => $el.css('visibility', 'hidden'));


        const tempCards = [];
        const startPositions = [];
        const endPositions = [];


        const spacing = window.innerWidth < 1024 ? 15 : 25;


        for (let i = 0; i < cards.length; i++) {
            const $tempCard = this.createCardElement(cards[i]);
            const startRect = fromElements[i][0].getBoundingClientRect();

            $tempCard.css({
                position: 'fixed',
                left: startRect.left + 'px',
                top: startRect.top + 'px',
                width: startRect.width + 'px',
                height: startRect.height + 'px',
                zIndex: 10001 + i,
                pointerEvents: 'none',
                margin: 0,
                boxSizing: 'border-box',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
            });
            $('body').append($tempCard);
            tempCards.push($tempCard);

            startPositions.push({
                x: startRect.left + startRect.width / 2,
                y: startRect.top + startRect.height / 2
            });


            const targetPos = this.getTableauPileTargetPosition(toPileIndex);

            endPositions.push({
                x: targetPos.x,
                y: targetPos.y + (i * spacing)
            });
        }


        await new Promise(resolve => requestAnimationFrame(resolve));


        await this.animateCardMovement(tempCards, startPositions, endPositions, {
            speedMultiplier: 2.4, // 加快一倍：1.2 * 2 = 2.4
            animationType: 'flying-to-tableau-real',
            staggerDelay: 0,
            baseSpeed: 1200
        });


        tempCards.forEach($el => $el.remove());
        this.isAnimatingCard = false;
    }

    isValidMove(targetPileIndex) {
        if (targetPileIndex === this.draggedFrom.index) return false;

        const targetPile = this.tableau[targetPileIndex];
        const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

        const cardToValidate = this.clickedCard || this.draggedCards[0];

        return this.canPlaceCard(cardToValidate, topCard);
    }

    async executeMove(targetPileIndex) {
        this.isAnimatingCard = true;

        const sourcePileIndex = this.draggedFrom.index;
        const movedCards = this.draggedCards;

        const sourcePile = this.tableau[sourcePileIndex];
        const cardsToMove = sourcePile.splice(-movedCards.length);

        this.tableau[targetPileIndex].push(...cardsToMove);

        let flippedCard = false;
        if (sourcePile.length > 0) {
            const newTopCard = sourcePile[sourcePile.length - 1];
            if (!newTopCard.faceUp) {
                newTopCard.faceUp = true;
                this.score += 5;
                flippedCard = true;
            }
        }

        this.moves++;
        this.score += 1;

        this.animateScoreUpdate();

        this.recordMove({
            type: 'move',
            from: this.draggedFrom,
            to: { type: 'tableau', index: targetPileIndex },
            cards: [...this.draggedCards],
            flippedCard: flippedCard
        });

        this.updateDisplay();
        this.isAnimatingCard = false;
    }

    // Add score update animation
    animateScoreUpdate() {
        const $scoreElement = $('#score');
        $scoreElement.addClass('score-updating');
        setTimeout(() => {
            $scoreElement.removeClass('score-updating');
        }, 500);
    }

    returnCardsToOriginalPosition() {
        if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) return;

        const transitionDuration = this.isMobile() ? 0.2 : 0.3;

        this.draggedElements.forEach((element, index) => {
            const originalPos = this.originalCardPositions[index];
            element.addClass('returning');
            element.css({
                left: originalPos.left + 'px',
                top: originalPos.top + 'px',
                transform: 'scale(1)',
                opacity: 1,
                zIndex: originalPos.zIndex || 'auto',
                transition: `all ${transitionDuration}s ease-out`
            });
        });

        setTimeout(() => {
            if (Array.isArray(this.draggedElements)) {
                this.draggedElements.forEach(element => {
                    element.removeClass('returning');
                    element.css({
                        transition: '',
                        transform: '',
                        opacity: ''
                    });
                });
            }
            this.updateDisplay();
        }, transitionDuration * 1000);
    }

    onKeyDown(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        switch(e.key) {
            case 'Escape':
                this.hideHelp();
                this.hideMessage();
                break;
            case 'h':
            case 'H':
                if (!e.ctrlKey && !e.metaKey) {
                    if (!this.canPerformAction('hint')) return;
                    this.setActionCooldown('hint');
                    this.showHint();
                }
                break;
            case 'n':
            case 'N':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    if (!this.canPerformAction('newGame')) return;
                    this.setActionCooldown('newGame');
                    this.newGame();
                }
                break;
            case 'z':
            case 'Z':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    if (!this.canPerformAction('undo')) return;
                    this.setActionCooldown('undo');
                    this.undoMove();
                }
                break;
            case ' ':
                e.preventDefault();
                if (!this.canPerformAction('dealStock')) return;
                this.setActionCooldown('dealStock');
                this.isProcessingAction = true;
                this.dealFromStock().finally(() => {
                    this.isProcessingAction = false;
                });
                break;
        }
    }

    undoMove() {
        if (this.moveHistory.length === 0) return;
        if (this.isAnimatingCard || this.isProcessingAction) return;
        
        const lastMove = this.moveHistory.pop();
        
        switch(lastMove.type) {
            case 'move':
                // Reverse the move
                const cardsToReverse = this.tableau[lastMove.to.index].splice(-lastMove.cards.length);
                this.tableau[lastMove.from.index].push(...cardsToReverse);
                
                // Handle face-down card that might have been flipped
                if (this.tableau[lastMove.from.index].length > lastMove.cards.length) {
                    const cardBelowMoved = this.tableau[lastMove.from.index][this.tableau[lastMove.from.index].length - lastMove.cards.length - 1];
                    if (cardBelowMoved && cardBelowMoved.faceUp && lastMove.flippedCard) {
                        cardBelowMoved.faceUp = false;
                        this.score -= 5;
                    }
                }
                
                this.moves--;
                this.score -= 1;
                break;
                
            case 'deal':
                // Reverse dealing cards
                for (let i = 0; i < 10 && this.tableau[i].length > 0; i++) {
                    const card = this.tableau[i].pop();
                    card.faceUp = false;
                    this.stock.push(card);
                }
                this.moves--;
                break;
        }
        
        this.updateDisplay();
    }

    showHint() {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        $('.hint-highlight').removeClass('hint-highlight');

        const possibleMove = this.findBestMove();
        if (possibleMove) {
            const $pile = $(`#tableau-${possibleMove.fromPile}`);
            const $card = $pile.find('.card').eq(possibleMove.cardIndex);
            $card.addClass('hint-highlight');

            setTimeout(() => {
                $('.hint-highlight').removeClass('hint-highlight');
            }, 2000);
        } else {
            // Check if game is deadlocked
            if (this.stock.length === 0 && !this.hasAnyValidMoves()) {
                this.showGameOverMessage();
            } else if (this.stock.length > 0) {
                // Suggest dealing from stock
                $('#stock').addClass('hint-highlight');
                setTimeout(() => {
                    $('#stock').removeClass('hint-highlight');
                }, 2000);
            }
        }
    }

    findBestMove() {
        let allPossibleMoves = [];

        // Find all possible moves
        for (let fromPile = 0; fromPile < 10; fromPile++) {
            const cardStates = this.calculateCardStates(fromPile);
            if (cardStates.movableCards.length === 0) continue;

            const pile = this.tableau[fromPile];

            // Only check movable cards, not all cards
            for (const movableCard of cardStates.movableCards) {
                const cardIndex = pile.findIndex(card => card.id === movableCard.id);
                if (cardIndex === -1) continue;

                const movableCards = this.getMovableCards(fromPile, cardIndex);
                if (movableCards.length === 0) continue;

                for (let toPile = 0; toPile < 10; toPile++) {
                    if (toPile === fromPile) continue;

                    const targetPile = this.tableau[toPile];
                    const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

                    if (this.canPlaceCard(movableCards[0], topCard)) {
                        // Calculate sequence length for this move
                        let sequenceLength = movableCards.length;
                        const movingCard = movableCards[0];

                        // Check backwards from the target pile's top card
                        for (let i = targetPile.length - 1; i >= 0; i--) {
                            const targetCard = targetPile[i];
                            if (!targetCard.faceUp) break;

                            const expectedValue = movingCard.value + sequenceLength;
                            if (targetCard.value === expectedValue && targetCard.suit === movingCard.suit) {
                                sequenceLength++;
                            } else {
                                break;
                            }
                        }

                        allPossibleMoves.push({
                            fromPile,
                            cardIndex,
                            toPile,
                            sequenceLength,
                            isEmpty: targetPile.length === 0,
                            movableCards
                        });
                    }
                }
            }
        }

        if (allPossibleMoves.length === 0) {
            return null;
        }

        // Sort by priority: longest sequence first, then non-empty piles, then empty piles
        allPossibleMoves.sort((a, b) => {
            // First priority: sequence length (descending)
            if (a.sequenceLength !== b.sequenceLength) {
                return b.sequenceLength - a.sequenceLength;
            }

            // Second priority: prefer non-empty piles over empty piles
            if (a.isEmpty !== b.isEmpty) {
                return a.isEmpty ? 1 : -1;
            }

            return 0;
        });

        return allPossibleMoves[0];
    }

    hasAnyValidMoves() {
        // Check if there are any valid card moves
        for (let fromPile = 0; fromPile < 10; fromPile++) {
            const cardStates = this.calculateCardStates(fromPile);
            if (cardStates.movableCards.length === 0) continue;

            const pile = this.tableau[fromPile];

            // Only check movable cards, not all cards
            for (const movableCard of cardStates.movableCards) {
                const cardIndex = pile.findIndex(card => card.id === movableCard.id);
                if (cardIndex === -1) continue;

                const movableCards = this.getMovableCards(fromPile, cardIndex);
                if (movableCards.length === 0) continue;

                for (let toPile = 0; toPile < 10; toPile++) {
                    if (toPile === fromPile) continue;

                    const targetPile = this.tableau[toPile];
                    const topCard = targetPile.length > 0 ? targetPile[targetPile.length - 1] : null;

                    if (this.canPlaceCard(movableCards[0], topCard)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    showGameOverMessage() {
        $('#messageTitle').text('🎯 Game Over');
        $('#messageText').text('No more moves available!');
        $('#finalScore').text(this.score);
        $('#finalTime').text(this.formatTime(this.elapsedTime));
        $('#finalMoves').text(this.moves);

        // Update message stats to include completed sequences
        $('.message-stats').html(`
            <div>Completed: <span>${this.completed.length}/8</span></div>
            <div>Score: <span>${this.score}</span></div>
            <div>Time: <span>${this.formatTime(this.elapsedTime)}</span></div>
            <div>Moves: <span>${this.moves}</span></div>
        `);

        // Update buttons with difficulty selection
        $('.message-buttons').html(`
            <div class="difficulty-selection" style="margin-bottom: 20px;">
                <p style="color: rgba(255,255,255,0.9); margin-bottom: 10px;">Select difficulty for new game:</p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <button id="newGameEasy" class="btn btn-success" style="font-size: 14px;">Easy (1 Suit)</button>
                    <button id="newGameMedium" class="btn btn-warning" style="font-size: 14px;">Medium (2 Suits)</button>
                    <button id="newGameHard" class="btn btn-danger" style="font-size: 14px;">Hard (4 Suits)</button>
                </div>
            </div>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="closeGameOverBtn" class="btn btn-secondary">Close</button>
            </div>
        `);

        $('#gameMessage').removeClass('hidden');

        $('#newGameEasy').on('click', () => {
            this.difficulty = 'easy';
            this.newGame();
            this.hideMessage();
        });

        $('#newGameMedium').on('click', () => {
            this.difficulty = 'medium';
            this.newGame();
            this.hideMessage();
        });

        $('#newGameHard').on('click', () => {
            this.difficulty = 'hard';
            this.newGame();
            this.hideMessage();
        });

        $('#closeGameOverBtn').on('click', () => {
            this.hideMessage();
        });
    }

    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    hideMessage() {
        $('#gameMessage').addClass('hidden');
    }

    showWinMessage() {
        $('#messageTitle').text('Congratulations!');
        $('#messageText').text('You completed Spider Solitaire!');
        $('#finalScore').text(this.score);
        $('#finalMoves').text(this.moves);
        
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');
        $('#finalTime').text(`${minutes}:${seconds}`);
        
        $('#gameMessage').removeClass('hidden');
    }

    autoFullscreen() {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement && !document.webkitFullscreenElement && 
            !document.mozFullScreenElement && !document.msFullscreenElement) {
            this.autoFullscreen();
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    updateFullscreenButton() {
        const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || 
                              document.mozFullScreenElement || document.msFullscreenElement);
        $('#fullscreenBtn').text(isFullscreen ? '⛶' : '⛶');
    }
}

// Initialize the game when DOM is loaded
$(document).ready(function() {
    window.game = new SpiderSolitaire();
});

